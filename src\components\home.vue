<script setup>
import { ref, computed, onUnmounted, h } from 'vue';
import { message } from 'ant-design-vue';
import { VideoCameraOutlined, CheckCircleOutlined, LoadingOutlined, PlayCircleOutlined, SmileOutlined, DownOutlined, ClockCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';

const mode = ref('face'); // 'face' or 'video'

const options = [
  {
    code: '实时',
    name: '实时',
  },
  {
    code: '回放',
    name: '回放',
    items: [
        
    ],
  },
];
const value = ref([]);
const dateTime = ref();
const value1 = ref();

// 判断是否选择了回放
const isPlayback = computed(() => {
  if (Array.isArray(value.value)) {
    return value.value.includes('回放');
  }
  return value.value === '回放';
});

// 事件卡片相关
const eventFilter = ref('unhandled'); // 'unhandled' | 'all'
const eventCollapse = ref({}); // 控制每个分组下拉
const eventList = ref([
  { id: 1, type: '异常闯入', time: '2024-06-12 10:23:11', status: '未处理', desc: '检测到异常闯入', camera: '摄像头1' },
  { id: 2, type: '设备故障', time: '2024-06-12 09:50:02', status: '未处理', desc: '摄像头2信号丢失', camera: '摄像头2' },
  { id: 3, type: '正常巡检', time: '2024-06-11 17:20:00', status: '已处理', desc: '例行巡检', camera: '摄像头1' },
  { id: 4, type: '异常闯入', time: '2024-06-11 15:10:45', status: '未处理', desc: '检测到异常闯入', camera: '摄像头3' },
  { id: 5, type: '设备故障', time: '2024-06-10 13:05:22', status: '已处理', desc: '摄像头1信号恢复', camera: '摄像头1' },
]);

const groupedEvents = computed(() => {
  // 按日期分组
  const groups = {};
  eventList.value.forEach(ev => {
    const date = ev.time.split(' ')[0];
    if (!groups[date]) groups[date] = [];
    groups[date].push(ev);
  });
  // 按时间倒序
  return Object.entries(groups).sort((a, b) => b[0].localeCompare(a[0]));
});

const filteredEvents = (events) => {
  if (eventFilter.value === 'unhandled') {
    return events.filter(ev => ev.status === '未处理');
  }
  return events;
};

const toggleCollapse = (date) => {
  eventCollapse.value[date] = !eventCollapse.value[date];
};

// 人脸识别相关
const isCameraActive = ref(false);
const videoElement = ref(null);
const canvasElement = ref(null);
const recognizedPerson = ref(null);
let videoStream = null;

const currentTime = computed(() => {
    const now = new Date();
    return now.getFullYear() + '-' + String(now.getMonth()+1).padStart(2,'0') + '-' + String(now.getDate()).padStart(2,'0') + ' ' + String(now.getHours()).padStart(2,'0') + ':' + String(now.getMinutes()).padStart(2,'0') + ':' + String(now.getSeconds()).padStart(2,'0');
});

const startCamera = async () => {
    try {
        videoStream = await navigator.mediaDevices.getUserMedia({ 
            video: { facingMode: 'user' } 
        });
        if (videoElement.value) {
            videoElement.value.srcObject = videoStream;
            isCameraActive.value = true;
            setTimeout(() => {
                simulateFaceRecognition();
            }, 3000);
        }
    } catch (err) {
        message.error('无法访问摄像头，请检查权限设置');
        console.error('摄像头访问错误：', err);
    }
};

const stopCamera = () => {
    if (videoStream) {
        videoStream.getTracks().forEach(track => track.stop());
        videoStream = null;
    }
    isCameraActive.value = false;
    recognizedPerson.value = null;
};

const simulateFaceRecognition = () => {
    const mockPerson = {
        name: '张三',
        id: 'S202200101',
        class: '计算机2班',
        role: 'student',
        time: currentTime.value,
        status: '识别成功',
    };
    if (videoElement.value && canvasElement.value) {
        const video = videoElement.value;
        const canvas = canvasElement.value;
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.strokeStyle = '#00ff00';
        ctx.lineWidth = 3;
        const faceX = canvas.width * 0.3;
        const faceY = canvas.height * 0.3;
        const faceWidth = canvas.width * 0.4;
        const faceHeight = canvas.height * 0.4;
        ctx.strokeRect(faceX, faceY, faceWidth, faceHeight);
        recognizedPerson.value = mockPerson;
    }
};

const faceDataList = ref([
  { name: '张三', id: 'S202200101', class: '计算机2班', time: '2024-06-12 10:23:11', status: '识别成功' },
  { name: '李四', id: 'S202200102', class: '计算机1班', time: '2024-06-12 09:50:02', status: '识别成功' },
  { name: '王五', id: 'S202200103', class: '设备部', time: '2024-06-11 17:20:00', status: '识别失败' },
]);

onUnmounted(() => {
    stopCamera();
});
</script>

<template>
  <div class="home-bg">
    <div class="home-container">
      <div class="mode-switch-card">
        <a-segmented
          v-model:value="mode"
          :options="[
            { label: '人脸识别', value: 'face', icon: () => h(SmileOutlined) },
            { label: '摄像头播放', value: 'video', icon: () => h(PlayCircleOutlined) }
          ]"
          size="large"
          class="mode-segmented"
        />
      </div>
      <transition name="fade-slide" mode="out-in">
        <div v-if="mode==='face'" key="face" class="main-row">
          <div class="face-recognition-section card">
            <h2 class="section-title"><SmileOutlined style="color:#1890ff; margin-right:8px;"/>实时人脸识别</h2>
            <div class="facial-recognition-container">
              <div class="camera-view">
                <div v-if="!isCameraActive" class="camera-placeholder">
                  <VideoCameraOutlined style="font-size: 48px; color: #bfbfbf; margin-bottom: 16px;" />
                  <p>点击启动摄像头进行识别</p>
                  <a-button type="primary" @click="startCamera">启动摄像头</a-button>
                </div>
                <div v-else class="camera-active">
                  <video ref="videoElement" autoplay class="camera-feed"></video>
                  <canvas ref="canvasElement" class="face-canvas"></canvas>
                </div>
              </div>
              <div class="recognition-results">
                <div v-if="recognizedPerson" class="recognition-success">
                  <CheckCircleOutlined style="color: #52c41a; font-size: 32px;" />
                  <h3>识别成功</h3>
                  <div class="person-info">
                    <p><strong>姓名：</strong>{{ recognizedPerson.name }}</p>
                    <p><strong>ID：</strong>{{ recognizedPerson.id }}</p>
                    <p><strong>班级/部门：</strong>{{ recognizedPerson.class }}</p>
                    <p><strong>识别时间：</strong>{{ currentTime }}</p>
                  </div>
                  <a-button type="primary" @click="stopCamera">关闭摄像头</a-button>
                </div>
                <div v-else-if="isCameraActive" class="recognition-waiting">
                  <LoadingOutlined spin style="font-size: 32px; color: #1890ff;" />
                  <p>请正对摄像头，系统正在识别...</p>
                  <a-button @click="stopCamera">取消</a-button>
                </div>
              </div>
            </div>
          </div>
          <div class="side-card">
            <div class="side-title"><CheckCircleOutlined style="color:#52c41a; margin-right:6px;"/>人脸识别数据</div>
            <div class="face-data-list">
              <div v-for="item in faceDataList" :key="item.id" class="face-data-item" :class="{'fail': item.status!=='识别成功'}">
                <div class="face-data-row">
                  <span class="face-name">{{ item.name }}</span>
                  <span class="face-status" :class="{'success': item.status==='识别成功'}">{{ item.status }}</span>
                </div>
                <div class="face-data-row small">
                  <span>ID: {{ item.id }}</span>
                  <span>{{ item.class }}</span>
                </div>
                <div class="face-data-row small">
                  <ClockCircleOutlined style="margin-right:4px;"/>{{ item.time }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else key="video" class="main-row">
          <div class="video-section card">
            <h2 class="section-title"><PlayCircleOutlined style="color:#faad14; margin-right:8px;"/>摄像头播放</h2>
            <div class="camera-select-section">
              <span>选择摄像头：</span>
              <a-select v-model:value="value1" style="width: 200px" placeholder="选择摄像头">
                <a-select-option value="1">摄像头1</a-select-option>
                <a-select-option value="2">摄像头2</a-select-option>
                <a-select-option value="3">摄像头3</a-select-option>
              </a-select>
              <a-cascader v-model:value="value" :field-names="{ label: 'name', value: 'code', children: 'items' }"
                :options="options" placeholder="请选择模式" style="margin-left: 16px; width: 200px;" />
            </div>
            <div v-if="isPlayback" style="margin: 16px 0;">
              <span>选择日期和时间：</span>
              <a-date-picker v-model:value="dateTime" show-time style="width: 240px" placeholder="请选择日期和时间" />
            </div>
            <div v-if="isPlayback" class="video-player-wrap">
              <video width="1000" controls>
                <source src="../../public/下载.mp4" type="video/mp4">
              </video>
            </div>
            <div v-else class="video-player-wrap">
              <div class="video-placeholder">
                <VideoCameraOutlined style="font-size: 64px; color: #bfbfbf; margin-bottom: 16px;" />
                <p class="placeholder-title">无法连接摄像头</p>
                <p class="placeholder-desc">请检查摄像头连接或选择正确的摄像头</p>
              </div>
            </div>
          </div>
          <div class="side-card">
            <div class="side-title"><ExclamationCircleOutlined style="color:#faad14; margin-right:6px;"/>检测事件</div>
            <div class="event-filter">
              <a-radio-group v-model:value="eventFilter" size="small">
                <a-radio-button value="unhandled">未处理</a-radio-button>
                <a-radio-button value="all">全部</a-radio-button>
              </a-radio-group>
            </div>
            <div class="event-list">
              <div v-for="([date, events]) in groupedEvents" :key="date" class="event-group">
                <div class="event-group-header" @click="toggleCollapse(date)">
                  <span><ClockCircleOutlined style="margin-right:4px;"/>{{ date }}</span>
                  <DownOutlined :class="{'rotated': eventCollapse[date]}" />
                </div>
                <transition name="fade-slide">
                  <div v-show="eventCollapse[date] !== false" class="event-group-body">
                    <div v-for="ev in filteredEvents(events)" :key="ev.id" class="event-item" :class="{'unhandled': ev.status==='未处理'}">
                      <div class="event-type">{{ ev.type }}</div>
                      <div class="event-desc">{{ ev.desc }}</div>
                      <div class="event-meta">
                        <span>{{ ev.time.split(' ')[1] }}</span>
                        <span>{{ ev.camera }}</span>
                        <span class="event-status" :class="{'unhandled': ev.status==='未处理'}">{{ ev.status }}</span>
                      </div>
                    </div>
                  </div>
                </transition>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<style scoped>
.home-bg {
  min-height: 100vh;
  background: linear-gradient(135deg, #e0e7ff 0%, #f0f5ff 100%);
  padding: 0;
}
.home-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 48px 16px 32px 16px;
}
.mode-switch-card {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}
.mode-segmented {
  background: #fff;
  border-radius: 32px;
  box-shadow: 0 4px 24px rgba(24,144,255,0.08);
  padding: 8px 24px;
  font-size: 18px;
}
.main-row {
  display: flex;
  gap: 40px;
  align-items: flex-start;
  justify-content: center;
  width: 100%;
}
.card {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08);
  padding: 40px 32px 32px 32px;
  margin-bottom: 32px;
  animation: fadeInCard 0.7s cubic-bezier(.4,0,.2,1);
  flex: 1 1 0;
  min-width: 0;
}
@keyframes fadeInCard {
  from { opacity: 0; transform: translateY(40px); }
  to { opacity: 1; transform: translateY(0); }
}
.section-title {
  font-size: 26px;
  font-weight: 600;
  margin-bottom: 32px;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
}
.facial-recognition-container {
  display: flex;
  gap: 40px;
  align-items: flex-start;
}
.camera-view {
  width: 480px;
  height: 360px;
  border: 1.5px solid #dbeafe;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(120deg, #f0f5ff 60%, #e0e7ff 100%);
  box-shadow: 0 2px 12px rgba(24,144,255,0.07);
  transition: box-shadow 0.2s;
}
.camera-view:hover {
  box-shadow: 0 6px 32px rgba(24,144,255,0.13);
}
.camera-placeholder {
  height: 100%;
  min-height: 360px;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: background 0.2s;
}
.camera-active {
  height: 100%;
  position: relative;
}
.camera-feed {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.face-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
.recognition-results {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  justify-content: center;
}
.recognition-success, .recognition-waiting {
  padding: 28px 18px;
  border: 1.5px solid #e6f7ff;
  border-radius: 12px;
  background: #fafdff;
  text-align: center;
  box-shadow: 0 2px 8px rgba(24,144,255,0.04);
  transition: box-shadow 0.2s;
}
.recognition-success:hover, .recognition-waiting:hover {
  box-shadow: 0 6px 24px rgba(82,196,26,0.10);
}
.person-info {
  margin: 16px 0;
  text-align: left;
  font-size: 17px;
}
.camera-select-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}
.video-player-wrap {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}
.fade-slide-enter-active, .fade-slide-leave-active {
  transition: all 0.5s cubic-bezier(.4,0,.2,1);
}
.fade-slide-enter-from, .fade-slide-leave-to {
  opacity: 0;
  transform: translateY(40px);
}
.video-placeholder {
  width: 480px;
  height: 360px;
  background: linear-gradient(120deg, #f0f5ff 60%, #e0e7ff 100%);
  border: 1.5px dashed #dbeafe;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 2px 12px rgba(24,144,255,0.07);
}
.placeholder-title {
  font-size: 22px;
  color: #888;
  font-weight: 600;
  margin-bottom: 8px;
}
.placeholder-desc {
  color: #aaa;
  font-size: 15px;
}
/* 右侧卡片样式 */
.side-card {
  width: 340px;
  min-width: 260px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08);
  padding: 28px 22px 18px 22px;
  margin-left: 0;
  display: flex;
  flex-direction: column;
  gap: 18px;
  max-height: 600px;
  overflow-y: auto;
}
.side-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}
.face-data-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.face-data-item {
  background: #f6faff;
  border-radius: 10px;
  padding: 12px 14px 8px 14px;
  box-shadow: 0 1px 4px rgba(24,144,255,0.04);
  margin-bottom: 2px;
  border-left: 4px solid #52c41a;
  transition: border-color 0.2s;
}
.face-data-item.fail {
  border-left-color: #ff7875;
  background: #fff6f6;
}
.face-data-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  margin-bottom: 2px;
}
.face-data-row.small {
  font-size: 13px;
  color: #888;
}
.face-status.success {
  color: #52c41a;
  font-weight: 600;
}
.face-status {
  font-weight: 600;
}
/* 事件卡片 */
.event-filter {
  margin-bottom: 10px;
}
.event-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.event-group {
  margin-bottom: 10px;
  background: #fafdff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(24,144,255,0.04);
}
.event-group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 15px;
  font-weight: 600;
  padding: 8px 10px 8px 8px;
  cursor: pointer;
  border-bottom: 1px solid #e6f7ff;
  user-select: none;
}
.event-group-header .anticon {
  transition: transform 0.3s;
}
.event-group-header .rotated {
  transform: rotate(180deg);
}
.event-group-body {
  padding: 8px 10px 6px 18px;
}
.event-item {
  padding: 7px 0 5px 0;
  border-bottom: 1px dashed #e6f7ff;
  font-size: 15px;
  color: #333;
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.event-item:last-child {
  border-bottom: none;
}
.event-item.unhandled {
  background: #fffbe6;
}
.event-type {
  font-weight: 600;
  color: #faad14;
}
.event-desc {
  color: #888;
  font-size: 14px;
}
.event-meta {
  display: flex;
  gap: 12px;
  font-size: 13px;
  color: #999;
  margin-top: 2px;
}
.event-status.unhandled {
  color: #fa541c;
  font-weight: 600;
}
.event-status {
  font-weight: 600;
}
</style>
