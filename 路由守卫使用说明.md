# 路由守卫实现说明

## 功能概述

已成功为您的安全系统项目添加了完整的路由守卫功能，实现了基于登录状态的路由访问控制。

## 实现的功能

### 1. 路由守卫 (`src/router/router.js`)
- **自动重定向**: 未登录用户访问受保护页面时自动跳转到登录页
- **登录后重定向**: 登录成功后自动跳转到原本要访问的页面
- **防止重复登录**: 已登录用户访问登录页时自动跳转到首页
- **404处理**: 未知路由自动重定向到登录页

### 2. 认证工具 (`src/utils/auth.js`)
- **用户状态管理**: 提供登录、注册、退出登录等功能
- **本地存储**: 使用localStorage存储用户token和信息
- **模拟认证**: 内置测试用户数据，支持快速测试

### 3. 登录组件 (`src/components/Login.vue`)
- **表单验证**: 完整的登录和注册表单验证
- **错误提示**: 友好的错误消息提示
- **加载状态**: 登录/注册过程中的加载状态显示

### 4. 应用主组件 (`src/App.vue`)
- **用户信息显示**: 顶部导航栏显示当前登录用户信息
- **退出登录**: 一键退出登录功能
- **状态同步**: 登录状态实时更新

## 测试用户账号

系统内置了以下测试账号，您可以直接使用：

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | 123456 | 管理员 | 系统管理员账号 |
| teacher | 123456 | 教师 | 教师账号 |
| student | 123456 | 学生 | 学生账号 |

## 使用方法

### 1. 启动项目
```bash
npm run dev
```

### 2. 测试路由守卫
1. 直接访问 `http://localhost:5173/home` - 应该自动跳转到登录页
2. 使用测试账号登录（如：admin/123456）
3. 登录成功后应该自动跳转到首页
4. 点击"退出登录"按钮测试退出功能

### 3. 受保护的路由
以下路由需要登录后才能访问：
- `/home` - 首页
- `/manage` - 后台管理
- `/per` - 人员信息
- `/device_Info` - 设备信息
- `/device_check` - 设备检查
- `/device_log` - 设备日志
- `/attendance` - 考勤管理

## 技术实现细节

### 路由元信息
```javascript
{
    path: '/home',
    name: 'Home',
    component: () => import('../components/home.vue'),
    meta: { requiresAuth: true }  // 标记需要认证
}
```

### 路由守卫逻辑
```javascript
router.beforeEach((to, _from, next) => {
    if (to.meta.requiresAuth) {
        if (isAuthenticated()) {
            next(); // 已登录，允许访问
        } else {
            next({ path: '/login', query: { redirect: to.fullPath } });
        }
    } else {
        if (to.path === '/login' && isAuthenticated()) {
            next('/home'); // 已登录用户访问登录页，重定向到首页
        } else {
            next(); // 允许访问
        }
    }
});
```

### 认证状态检查
```javascript
const isAuthenticated = () => {
    const token = localStorage.getItem('userToken');
    const userInfo = localStorage.getItem('userInfo');
    return !!(token && userInfo);
};
```

## 自定义配置

### 添加新的受保护路由
1. 在路由配置中添加 `meta: { requiresAuth: true }`
2. 路由守卫会自动处理认证检查

### 修改测试用户
编辑 `src/utils/auth.js` 中的 `mockUsers` 数组

### 集成真实API
替换 `src/utils/auth.js` 中的模拟函数为真实的API调用

## 注意事项

1. **安全性**: 当前使用localStorage存储认证信息，生产环境建议使用更安全的方式
2. **Token过期**: 当前未实现token过期检查，建议在生产环境中添加
3. **权限控制**: 可以基于用户角色进一步实现细粒度的权限控制

## 下一步建议

1. 集成真实的后端认证API
2. 添加token自动刷新机制
3. 实现基于角色的权限控制
4. 添加记住登录状态功能
5. 优化错误处理和用户体验
