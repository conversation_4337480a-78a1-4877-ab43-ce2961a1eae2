<template>
  <header class="main-navbar">
    <div class="navbar-left">
      基于人工智能的实训室智慧管理系统
    </div>
    <div class="navbar-right" v-if="isAuthenticated">
      <button class="upload-trigger-btn" @click="openModal">
        <img :src="currentAvatar" class="current-avatar" alt="当前头像" />
        <p>{{ userInfo?.name || 'name' }}</p>
      </button>
      <router-link to="/manage" class="manage-link">后台管理</router-link>
      <button class="logout-btn" @click="handleLogout">退出登录</button>
    </div>
  </header>
  <article>
    <!-- 头像上传弹框 -->
    <div class="modal-overlay" :class="{ active: showModal }" @click="closeModal">
      <div class="modal-container" @click.stop>
        <div class="modal-header">
          <h2>头像上传与裁剪</h2>
          <button class="close-btn" @click="closeModal">×</button>
        </div>

        <div class="modal-content">
          <!-- 上传区域 -->
          <div class="upload-section">
            <div class="upload-area" @click="triggerFileInput" @dragover="handleDragOver" @drop="handleDrop">
              <input type="file" ref="fileInput" @change="handleFileSelect" accept="image/*" style="display: none;">
              <div class="upload-icon">📤</div>
              <h3>选择或拖拽图片</h3>
              <p>支持 JPG、PNG 格式，文件大小不超过 5MB</p>
            </div>

            <div class="controls">
              <button class="btn btn-secondary" @click="resetAll">
                🔄 重置
              </button>
            </div>
          </div>

          <!-- 预览和裁剪区域 -->
          <div class="preview-section" v-if="previewImage">
            <h3 class="section-title">裁剪预览</h3>

            <div class="preview-container" ref="previewContainer">
              <img :src="previewImage" ref="previewImg" class="preview-img"
                :style="{ transform: `translate(-50%, -50%) rotate(${rotation}deg)` }">
              <div ref="cropBox" class="crop-box" :style="cropBoxStyle" @mousedown="startCropBoxDrag">
                <div class="crop-handle handle-nw" @mousedown.stop="startResize('nw', $event)"></div>
                <div class="crop-handle handle-ne" @mousedown.stop="startResize('ne', $event)"></div>
                <div class="crop-handle handle-sw" @mousedown.stop="startResize('sw', $event)"></div>
                <div class="crop-handle handle-se" @mousedown.stop="startResize('se', $event)"></div>
              </div>
            </div>
            <div class="controls">
              <button class="btn btn-primary" @click="cropImage">
                ✂️ 裁剪并确认
              </button>
              <button class="btn btn-secondary" @click="rotateImage">
                🔄 旋转图片
              </button>
            </div>

            <!-- 头像预览 -->
            <div class="result-container" v-if="croppedAvatar">
              <img :src="croppedAvatar" class="avatar-preview">
              <div class="final-controls">
                <button class="btn btn-primary" @click="confirmAvatar">
                  ✅ 确认使用
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </article>
  <div id="app">
    <router-view></router-view>
  </div>
</template>
<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { getUserInfo, logout, isAuthenticated as checkAuth } from './utils/auth.js';

// 路由实例
const router = useRouter();

// 认证相关数据
const isAuthenticated = ref(false);
const userInfo = ref(null);

// 响应式数据
const showModal = ref(false);
const currentAvatar = ref('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiByeD0iNjAiIGZpbGw9IiNmMGY4ZmYiLz4KPHN2ZyB4PSIzMCIgeT0iMzAiIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSIjMjZkMGNlIj4KPHA+VXNlcjwvcD4KPC9zdmc+Cjwvc3ZnPgo='); // 默认头像
const previewImage = ref('');
const croppedAvatar = ref('');
const rotation = ref(0);
const cropSize = ref(200);

// DOM引用
const fileInput = ref(null);
const previewImg = ref(null);
const cropBox = ref(null);
const previewContainer = ref(null);

// 裁剪框数据
const cropBoxData = reactive({
  x: 0,
  y: 0,
  width: 200,
  height: 200,
  dragging: false,
  resizing: false,
  dragStartX: 0,
  dragStartY: 0,
  resizeDir: ''
});

// 当前图片对象
let currentImage = null;

// 计算裁剪框样式
const cropBoxStyle = computed(() => ({
  left: cropBoxData.x + 'px',
  top: cropBoxData.y + 'px',
  width: cropBoxData.width + 'px',
  height: cropBoxData.height + 'px',
  display: previewImage.value ? 'block' : 'none'
}));

// 弹框控制
const openModal = () => {
  showModal.value = true;
};

const closeModal = () => {
  showModal.value = false;
  resetAll();
};

// 文件选择
const triggerFileInput = () => {
  fileInput.value?.click();
};

const handleFileSelect = (e) => {
  const file = e.target.files[0];
  if (file && file.type.match('image.*')) {
    if (file.size > 5 * 1024 * 1024) {
      alert('文件大小不能超过5MB');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      previewImage.value = e.target.result;
      currentImage = new Image();
      currentImage.src = e.target.result;
      currentImage.onload = () => {
        resetCropBox();
      };
    };
    reader.readAsDataURL(file);
  }
};

// 拖放处理
const handleDragOver = (e) => {
  e.preventDefault();
  e.stopPropagation();
};

const handleDrop = (e) => {
  e.preventDefault();
  e.stopPropagation();

  const file = e.dataTransfer.files[0];
  if (file && file.type.match('image.*')) {
    fileInput.value.files = e.dataTransfer.files;
    handleFileSelect({ target: { files: e.dataTransfer.files } });
  }
};

// 重置裁剪框
const resetCropBox = () => {
  if (!previewContainer.value) return;

  const containerRect = previewContainer.value.getBoundingClientRect();
  const centerX = (containerRect.width - cropSize.value) / 2;
  const centerY = (containerRect.height - cropSize.value) / 2;

  cropBoxData.x = centerX;
  cropBoxData.y = centerY;
  cropBoxData.width = cropSize.value;
  cropBoxData.height = cropSize.value;
};

// 更新裁剪尺寸
const updateCropSize = () => {
  const size = Math.max(50, Math.min(cropSize.value, 300));
  cropSize.value = size;
  cropBoxData.width = size;
  cropBoxData.height = size;
  if (currentImage) {
    resetCropBox();
  }
};

// 裁剪框拖拽
const startCropBoxDrag = (e) => {
  if (!currentImage) return;

  cropBoxData.dragging = true;
  cropBoxData.dragStartX = e.clientX - cropBoxData.x;
  cropBoxData.dragStartY = e.clientY - cropBoxData.y;

  document.addEventListener('mousemove', handleCropBoxMove);
  document.addEventListener('mouseup', stopCropBoxDrag);
};

const startResize = (direction, e) => {
  if (!currentImage) return;

  cropBoxData.resizing = true;
  cropBoxData.resizeDir = direction;
  cropBoxData.dragStartX = e.clientX - cropBoxData.x;
  cropBoxData.dragStartY = e.clientY - cropBoxData.y;

  document.addEventListener('mousemove', handleCropBoxMove);
  document.addEventListener('mouseup', stopCropBoxDrag);
};

const handleCropBoxMove = (e) => {
  if (!currentImage || !previewContainer.value) return;

  const containerRect = previewContainer.value.getBoundingClientRect();
  const maxX = containerRect.width - cropBoxData.width;
  const maxY = containerRect.height - cropBoxData.height;

  if (cropBoxData.dragging) {
    let newX = e.clientX - cropBoxData.dragStartX;
    let newY = e.clientY - cropBoxData.dragStartY;

    newX = Math.max(0, Math.min(newX, maxX));
    newY = Math.max(0, Math.min(newY, maxY));

    cropBoxData.x = newX;
    cropBoxData.y = newY;
  } else if (cropBoxData.resizing) {
    const dx = e.clientX - (cropBoxData.x + cropBoxData.dragStartX);
    const dy = e.clientY - (cropBoxData.y + cropBoxData.dragStartY);

    switch (cropBoxData.resizeDir) {
      case 'nw':
        cropBoxData.width = Math.max(50, cropBoxData.width - dx);
        cropBoxData.height = Math.max(50, cropBoxData.height - dy);
        cropBoxData.x += dx;
        cropBoxData.y += dy;
        break;
      case 'ne':
        cropBoxData.width = Math.max(50, cropBoxData.width + dx);
        cropBoxData.height = Math.max(50, cropBoxData.height - dy);
        cropBoxData.y += dy;
        break;
      case 'sw':
        cropBoxData.width = Math.max(50, cropBoxData.width - dx);
        cropBoxData.height = Math.max(50, cropBoxData.height + dy);
        cropBoxData.x += dx;
        break;
      case 'se':
        cropBoxData.width = Math.max(50, cropBoxData.width + dx);
        cropBoxData.height = Math.max(50, cropBoxData.height + dy);
        break;
    }

    cropBoxData.dragStartX = e.clientX - cropBoxData.x;
    cropBoxData.dragStartY = e.clientY - cropBoxData.y;

    cropBoxData.x = Math.max(0, Math.min(cropBoxData.x, maxX));
    cropBoxData.y = Math.max(0, Math.min(cropBoxData.y, maxY));
  }
};

const stopCropBoxDrag = () => {
  cropBoxData.dragging = false;
  cropBoxData.resizing = false;
  document.removeEventListener('mousemove', handleCropBoxMove);
  document.removeEventListener('mouseup', stopCropBoxDrag);
};

// 旋转图片
const rotateImage = () => {
  if (!currentImage) return;
  rotation.value = (rotation.value + 90) % 360;
};

// 裁剪图片
const cropImage = () => {

  const containerRect = previewContainer.value.getBoundingClientRect();
  const imgRect = previewImg.value.getBoundingClientRect();

  const imgX = (containerRect.width - imgRect.width) / 2;
  const imgY = (containerRect.height - imgRect.height) / 2;

  const cropX = cropBoxData.x - imgX;
  const cropY = cropBoxData.y - imgY;

  const scaleX = currentImage.width / imgRect.width;
  const scaleY = currentImage.height / imgRect.height;

  const actualCropX = cropX * scaleX;
  const actualCropY = cropY * scaleY;
  const actualCropWidth = cropBoxData.width * scaleX;
  const actualCropHeight = cropBoxData.height * scaleY;

  const canvas = document.createElement('canvas');
  canvas.width = actualCropWidth;
  canvas.height = actualCropHeight;
  const ctx = canvas.getContext('2d');

  ctx.save();
  ctx.translate(canvas.width / 2, canvas.height / 2);
  ctx.rotate(rotation.value * Math.PI / 180);

  ctx.drawImage(
    currentImage,
    actualCropX, actualCropY, actualCropWidth, actualCropHeight,
    -canvas.width / 2, -canvas.height / 2, canvas.width, canvas.height
  );
  ctx.restore();

  const circleCanvas = document.createElement('canvas');
  circleCanvas.width = actualCropWidth;
  circleCanvas.height = actualCropHeight;
  const circleCtx = circleCanvas.getContext('2d');

  circleCtx.beginPath();
  circleCtx.arc(
    actualCropWidth / 2,
    actualCropHeight / 2,
    Math.min(actualCropWidth, actualCropHeight) / 2,
    0,
    Math.PI * 2
  );
  circleCtx.closePath();
  circleCtx.clip();
  circleCtx.drawImage(canvas, 0, 0);

  croppedAvatar.value = circleCanvas.toDataURL('image/png');
};

// 确认头像
const confirmAvatar = () => {
  if (croppedAvatar.value) {
    currentAvatar.value = croppedAvatar.value;
    closeModal();
  }
};

// 重置所有
const resetAll = () => {
  if (fileInput.value) {
    fileInput.value.value = '';
  }
  previewImage.value = '';
  croppedAvatar.value = '';
  currentImage = null;
  rotation.value = 0;
  cropSize.value = 200;
  cropBoxData.x = 0;
  cropBoxData.y = 0;
  cropBoxData.width = 200;
  cropBoxData.height = 200;
};
// 初始化用户信息
const initUserInfo = () => {
  isAuthenticated.value = checkAuth();
  if (isAuthenticated.value) {
    userInfo.value = getUserInfo();
    // 如果用户有自定义头像，使用用户头像
    if (userInfo.value?.avatar) {
      currentAvatar.value = userInfo.value.avatar;
    }
  }
};

// 退出登录
const handleLogout = async () => {
  try {
    await logout();
    isAuthenticated.value = false;
    userInfo.value = null;
    // 重置头像为默认头像
    currentAvatar.value = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiByeD0iNjAiIGZpbGw9IiNmMGY4ZmYiLz4KPHN2ZyB4PSIzMCIgeT0iMzAiIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSIjMjZkMGNlIj4KPHA+VXNlcjwvcD4KPC9zdmc+Cjwvc3ZnPgo=';
    router.push('/login');
  } catch (error) {
    console.error('退出登录失败:', error);
  }
};

// 组件挂载时初始化用户信息
onMounted(() => {
  initUserInfo();
});
</script>
<style scoped>
.main-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40px;
  height: 70px;
  background: linear-gradient(90deg, #1a2980 0%, #26d0ce 100%);
  color: #fff;
  border-radius: 0 0 18px 18px;
  box-shadow: 0 4px 18px rgba(38, 208, 206, 0.15);
  margin-bottom: 18px;
}
.navbar-left {
  font-size: 1.6rem;
  font-weight: bold;
  letter-spacing: 2px;
}
.navbar-right {
  display: flex;
  align-items: center;
  gap: 24px;
}
.upload-trigger-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0 10px;
  border-radius: 30px;
  transition: background 0.2s;
  height: 48px;
}
.upload-trigger-btn:hover {
  background: rgba(255,255,255,0.08);
}
.current-avatar {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  object-fit: cover;
  background-color: #f0f8ff;
  border: 2px solid #fff;
  box-shadow: 0 2px 8px rgba(38, 208, 206, 0.12);
}
.upload-trigger-btn p {
  margin: 0;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
}
.manage-link {
  color: #fff;
  background: rgba(38, 208, 206, 0.18);
  padding: 8px 22px;
  border-radius: 22px;
  font-weight: 500;
  text-decoration: none;
  transition: background 0.2s, color 0.2s;
  border: 1px solid rgba(255,255,255,0.18);
}
.manage-link:hover {
  background: #fff;
  color: #1a2980;
}
/* 照妖镜 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.modal-container {
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  background-color: white;
  border-radius: 20px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  transform: scale(0.8);
  transition: transform 0.3s ease;
  overflow-y: auto;
}

.modal-overlay.active .modal-container {
  transform: scale(1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background: linear-gradient(90deg, #1a2980, #26d0ce);
  color: white;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.modal-content {
  padding: 30px;
}

.upload-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 1.4rem;
  color: #1a2980;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 2px solid #26d0ce;
}

.upload-area {
  border: 3px dashed #26d0ce;
  border-radius: 15px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: rgba(38, 208, 206, 0.05);
  margin-bottom: 20px;
}

.upload-area:hover {
  background-color: rgba(38, 208, 206, 0.1);
  transform: translateY(-3px);
}

.upload-icon {
  font-size: 3.5rem;
  margin-bottom: 15px;
}

.upload-area h3 {
  color: #1a2980;
  margin-bottom: 10px;
}

.upload-area p {
  color: #666;
  font-size: 0.95rem;
}

.controls {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.btn {
  padding: 12px 25px;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}
/* 毁灭程序 */
.btn-primary {
  background: linear-gradient(90deg, #1a2980, #26d0ce);
  color: white;
  box-shadow: 0 4px 15px rgba(26, 41, 128, 0.3);
}
.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(26, 41, 128, 0.4);
}
/* 时间胶囊 */
.btn-secondary {
  background: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}
.btn-secondary:hover {
  background: #eaeaea;
}
/* 百鬼夜行图所在 */
.preview-container {
  position: relative;
  width: 100%;
  height: 400px;
  border: 2px solid #26d0ce;
  border-radius: 10px;
  overflow: hidden;
  background-color: #f0f8ff;
  margin-bottom: 20px;
}
.preview-img {
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
}
/* 纸人存在吗 */
.crop-box {
  position: absolute;
  border: 2px solid #ff4081;
  cursor: move;
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
  z-index: 10;
}
/* 纸人的四肢是否健全 */
.crop-handle {
  position: absolute;
  width: 12px;
  height: 12px;
  background: #ff4081;
  /* border-radius: 50%; */
  z-index: 11;
}
/* 纸人的四肢脉络 */
.handle-nw {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}
.handle-ne {
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}
.handle-sw {
  bottom: -6px;
  left: -6px;
  cursor: sw-resize;
}
.handle-se {
  bottom: -6px;
  right: -6px;
  cursor: se-resize;
}
/* 画皮 */
.avatar-preview {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  border: 4px solid #26d0ce;
  margin: 0 auto 20px;
  object-fit: cover;
  background-color: #f0f8ff;
  display: block;
}

/* 退出登录按钮样式 */
.logout-btn {
  padding: 8px 16px;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 12px;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.logout-btn:hover {
  background: linear-gradient(135deg, #ff5252, #d32f2f);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
}

.logout-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(255, 107, 107, 0.3);
}
</style>
