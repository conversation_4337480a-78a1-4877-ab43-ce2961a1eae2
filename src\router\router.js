import { createRouter, createWebHistory } from "vue-router";
const router = createRouter({
    history:createWebHistory(),
    routes:[
        {
            path:'/',
            redirect:'/home'
        },
        {
            path:'/home',
            component:()=>import('../components/home.vue')
        },
        {
            path:'/manage',
            component:()=>import('../components/manage.vue')
        },
        {
            path:'/per',
            component:()=>import('../components/Per_Info.vue')
        },
        {
            path:'/device_Info',
            component:()=>import('../components/Device_Info.vue')
        },
        {
            path:'/device_check',
            component:()=>import('../components/Device_check.vue')
        },
        {
            path:'/device_log',
            component:()=>import('../components/Device_log.vue')
        },
        {
            path:'/attendance',
            component:()=>import('../components/Attendance.vue')
        },
        {
            path:'/login',
            component:()=>import('../components/Login.vue')
        },
    ]
})
export default router
