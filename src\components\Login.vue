<template>
  <div class="body">
    <div class="main-box">
      <div :class="['container', 'container-register', { 'is-txl': isLogin }]">
        <form @submit.prevent="handleRegister">
          <h2 class="title">注册</h2>
          <div class="form__icons">
            <img class="form__icon" src="../assets/images/wechat.png" alt="微信登录">
            <img class="form__icon" src="../assets/images/alipay.png" alt="支付宝登录">
            <img class="form__icon" src="../assets/images/QQ.png" alt="QQ登录">
          </div>
          <span class="text">或使用邮箱进行注册</span>
          <input
            class="form__input"
            type="text"
            placeholder="请输入用户名"
            v-model="registerForm.username"
            required
          />
          <input
            class="form__input"
            type="email"
            placeholder="请输入邮箱"
            v-model="registerForm.email"
            required
          />
          <input
            class="form__input"
            type="password"
            placeholder="请输入密码"
            v-model="registerForm.password"
            required
          />
          <input
            class="form__input"
            type="password"
            placeholder="确认密码"
            v-model="registerForm.confirmPassword"
            required
          />
          <button
            type="submit"
            class="form__button"
            :disabled="registerLoading"
          >
            {{ registerLoading ? '注册中...' : '立即注册' }}
          </button>
        </form>
      </div>
      <div :class="['container', 'container-login', { 'is-txl is-z200': isLogin }]">
        <form @submit.prevent="handleLogin">
          <h2 class="title">登录</h2>
          <div class="form__icons">
            <img class="form__icon" src="../assets/images/wechat.png" alt="微信登录">
            <img class="form__icon" src="../assets/images/alipay.png" alt="支付宝登录">
            <img class="form__icon" src="../assets/images/QQ.png" alt="QQ登录">
          </div>
          <span class="text">或使用用户名登录</span>
          <input
            class="form__input"
            type="text"
            placeholder="用户名/邮箱/手机号"
            v-model="loginForm.username"
            required
          />
          <input
            class="form__input"
            type="password"
            placeholder="请输入密码"
            v-model="loginForm.password"
            required
          />
          <button
            type="submit"
            class="form__button"
            :disabled="loginLoading"
          >
            {{ loginLoading ? '登录中...' : '立即登录' }}
          </button>
        </form>
      </div>
      <div :class="['switch', { 'login': isLogin }]">
        <div class="switch__circle"></div>
        <div class="switch__circle switch__circle_top"></div>
        <div class="switch__container">
          <h2>{{ isLogin ? '您好 !' : '欢迎回来 !' }}</h2>
          <p>
            {{
              isLogin
                  ? '如果您还没有账号，请点击下方立即注册按钮进行账号注册'
                  : '如果您已经注册过账号，请点击下方立即登录按钮进行登录'
            }}
          </p>
          <div class="form__button" @click="isLogin = !isLogin">
            {{ isLogin ? '立即注册' : '立即登录' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { login, register, setAuth } from '../utils/auth.js';

export default {
  name: 'Login',
  data() {
    return {
      isLogin: true,
      loginLoading: false,
      registerLoading: false,
      loginForm: {
        username: '',
        password: '',
      },
      registerForm: {
        username: '',
        email: '',
        password: '',
        confirmPassword: '',
      },
    }
  },
  methods: {
    // 处理登录
    async handleLogin() {
      if (!this.loginForm.username || !this.loginForm.password) {
        this.showMessage('请填写用户名和密码', 'error');
        return;
      }

      this.loginLoading = true;

      try {
        const result = await login(this.loginForm.username, this.loginForm.password);

        if (result.success) {
          // 保存登录信息
          setAuth(result.token, result.userInfo);

          this.showMessage(result.message, 'success');

          // 获取重定向路径，如果没有则默认跳转到首页
          const redirect = this.$route.query.redirect || '/home';
          this.$router.push(redirect);
        } else {
          this.showMessage(result.message, 'error');
        }
      } catch (error) {
        console.error('登录错误:', error);
        this.showMessage('登录失败，请稍后重试', 'error');
      } finally {
        this.loginLoading = false;
      }
    },

    // 处理注册
    async handleRegister() {
      if (!this.registerForm.username || !this.registerForm.email || !this.registerForm.password) {
        this.showMessage('请填写完整信息', 'error');
        return;
      }

      if (this.registerForm.password !== this.registerForm.confirmPassword) {
        this.showMessage('两次输入的密码不一致', 'error');
        return;
      }

      this.registerLoading = true;

      try {
        const result = await register(this.registerForm);

        if (result.success) {
          this.showMessage(result.message, 'success');
          // 注册成功后切换到登录表单
          this.isLogin = true;
          // 清空注册表单
          this.registerForm = {
            username: '',
            email: '',
            password: '',
            confirmPassword: '',
          };
        } else {
          this.showMessage(result.message, 'error');
        }
      } catch (error) {
        console.error('注册错误:', error);
        this.showMessage('注册失败，请稍后重试', 'error');
      } finally {
        this.registerLoading = false;
      }
    },

    // 显示消息提示
    showMessage(message, type = 'info') {
      // 创建消息提示元素
      const messageEl = document.createElement('div');
      messageEl.className = `message-toast message-${type}`;
      messageEl.textContent = message;

      // 添加样式
      Object.assign(messageEl.style, {
        position: 'fixed',
        top: '20px',
        left: '50%',
        transform: 'translateX(-50%)',
        padding: '12px 24px',
        borderRadius: '4px',
        color: '#fff',
        fontSize: '14px',
        zIndex: '9999',
        backgroundColor: type === 'success' ? '#52c41a' : type === 'error' ? '#ff4d4f' : '#1890ff',
        boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
      });

      document.body.appendChild(messageEl);

      // 3秒后自动移除
      setTimeout(() => {
        if (messageEl.parentNode) {
          messageEl.parentNode.removeChild(messageEl);
        }
      }, 3000);
    }
  },
}
</script>

<style lang="scss" scoped>
.body {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  background-image: url("../assets/images/background.jpg");
  color: #a0a5a8;

}
.main-box {
  position: relative;
  width: 1000px;
  min-width: 1000px;
  min-height: 600px;
  height: 600px;
  padding: 25px;
  background-color: #ecf0f3;
  box-shadow: 1px 1px 100px 10PX #ecf0f3;
  border-radius: 12px;
  overflow: hidden;

  .container {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    width: 600px;
    height: 100%;
    padding: 25px;
    background-color: #ecf0f3;
    transition: all 1.25s;

    form {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      width: 100%;
      height: 100%;
      color: #a0a5a8;

      .form__icon {
        object-fit: contain;
        width: 30px;
        margin: 0 5px;
        opacity: .5;
        transition: .15s;

        &:hover {
          opacity: 1;
          transition: .15s;
          cursor: pointer;

        }
      }

      .title {
        font-size: 34px;
        font-weight: 700;
        line-height: 3;
        color: #181818;
      }

      .text {
        margin-top: 30px;
        margin-bottom: 12px;
      }

      .form__input {
        width: 350px;
        height: 40px;
        margin: 4px 0;
        padding-left: 25px;
        font-size: 13px;
        letter-spacing: 0.15px;
        border: none;
        outline: none;
        // font-family: 'Montserrat', sans-serif;
        background-color: #ecf0f3;
        transition: 0.25s ease;
        border-radius: 8px;
        box-shadow: inset 2px 2px 4px #d1d9e6, inset -2px -2px 4px #f9f9f9;

        &::placeholder {
          color: #a0a5a8;
        }
      }
    }
  }

  .container-register {
    z-index: 100;
    left: calc(100% - 600px);
  }

  .container-login {
    left: calc(100% - 600px);
    z-index: 0;
  }

  .is-txl {
    left: 0;
    transition: 1.25s;
    transform-origin: right;
  }

  .is-z200 {
    z-index: 200;
    transition: 1.25s;
  }

  .switch {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 300px;
    padding: 50px;
    z-index: 200;
    transition: 1.25s;
    background-color: #ecf0f3;
    overflow: hidden;
    box-shadow: 4px 4px 10px #d1d9e6, -4px -4px 10px #f9f9f9;
    color: #a0a5a8;

    .switch__circle {
      position: absolute;
      width: 500px;
      height: 500px;
      border-radius: 50%;
      background-color: #ecf0f3;
      box-shadow: inset 8px 8px 12px #d1d9e6, inset -8px -8px 12px #f9f9f9;
      bottom: -60%;
      left: -60%;
      transition: 1.25s;
    }

    .switch__circle_top {
      top: -30%;
      left: 60%;
      width: 300px;
      height: 300px;
    }

    .switch__container {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      position: absolute;
      width: 400px;
      padding: 50px 55px;
      transition: 1.25s;

      h2 {
        font-size: 34px;
        font-weight: 700;
        line-height: 3;
        color: #181818;
      }

      p {
        font-size: 14px;
        letter-spacing: 0.25px;
        text-align: center;
        line-height: 1.6;
      }
    }
  }

  .login {
    left: calc(100% - 400px);

    .switch__circle {
      left: 0;
    }
  }

  .form__button {
    width: 180px;
    height: 50px;
    border-radius: 25px;
    margin-top: 50px;
    text-align: center;
    line-height: 50px;
    font-size: 14px;
    letter-spacing: 2px;
    background-color: #4b70e2;
    color: #f9f9f9;
    cursor: pointer;
    box-shadow: 8px 8px 16px #d1d9e6, -8px -8px 16px #f9f9f9;

    &:hover {
      box-shadow: 2px 2px 3px 0 rgba(255, 255, 255, 50%),
      -2px -2px 3px 0 rgba(116, 125, 136, 50%),
      inset -2px -2px 3px 0 rgba(255, 255, 255, 20%),
      inset 2px 2px 3px 0 rgba(0, 0, 0, 30%);
    }
  }
}
</style>

